root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-23 00:03:15
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-23 00:03:15
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\simple_hft
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY']
market_type_scanner - INFO - Delta-based filtering: Created delta map with 14 entries
market_type_scanner - INFO - Pivot point mode: Added 14 options for NIFTY
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 14
market_type_scanner - INFO - Pivot point mode: Fetching market data for 14 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 14 symbols
fyers_client - INFO - Quote fetching progress: 14/14 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 14/14 symbols (100.0% batch success rate) in 0.7s
market_type_scanner - INFO - Volume filter: 14/14 symbols passed
market_type_scanner - INFO - LTP filter: 14/14 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 14 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/14 symbols (7.1%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 10/14 symbols (71.4%) - 9 successful calculations
market_type_scanner - INFO - Pivot point calculation progress: 14/14 symbols (100.0%) - 13 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 14/14 symbols successful (100.0% success rate)
market_type_scanner - INFO - Market data contains 14/14 symbols with pivot data
market_type_scanner - INFO - Built pivot data map with 1 entries from 1 symbols
pivot_point_integration - INFO - Filtered to top 1 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 1 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.7s
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250723_000443.csv
report_generator - INFO - Report contains 1 symbols
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250723_000443.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250723_000443.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250723_000443.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 1
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250723_000443.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250723_000443.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250723_000445.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-23 00:04:45
unified_scanner - INFO - Total symbols found: 1
unified_scanner - INFO -   - OPTIONS symbols: 1
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250723_000445.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-23 00:09:58
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-23 00:09:58
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY']
market_type_scanner - INFO - Delta-based filtering: Created delta map with 14 entries
market_type_scanner - INFO - Pivot point mode: Added 14 options for NIFTY
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 14
market_type_scanner - INFO - Pivot point mode: Fetching market data for 14 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 14 symbols
fyers_client - INFO - Quote fetching progress: 14/14 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 14/14 symbols (100.0% batch success rate) in 0.5s
market_type_scanner - INFO - Volume filter: 14/14 symbols passed
market_type_scanner - INFO - LTP filter: 14/14 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 14 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/14 symbols (7.1%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 10/14 symbols (71.4%) - 9 successful calculations
market_type_scanner - INFO - Pivot point calculation progress: 14/14 symbols (100.0%) - 13 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 14/14 symbols successful (100.0% success rate)
market_type_scanner - INFO - Market data contains 14/14 symbols with pivot data
market_type_scanner - INFO - Built pivot data map with 1 entries from 1 symbols
pivot_point_integration - INFO - Filtered to top 1 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 1 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250723_001006.csv
report_generator - INFO - Report contains 1 symbols
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250723_001006.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250723_001006.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250723_001006.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 1
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250723_001006.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250723_001006.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250723_001007.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-23 00:10:07
unified_scanner - INFO - Total symbols found: 1
unified_scanner - INFO -   - OPTIONS symbols: 1
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250723_001007.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-23 00:11:35
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-23 00:11:35
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY']
market_type_scanner - INFO - Delta-based filtering: Created delta map with 14 entries
market_type_scanner - INFO - Pivot point mode: Added 14 options for NIFTY
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 14
market_type_scanner - INFO - Pivot point mode: Fetching market data for 14 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 14 symbols
fyers_client - INFO - Quote fetching progress: 14/14 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 14/14 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 14/14 symbols passed
market_type_scanner - INFO - LTP filter: 14/14 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 14 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/14 symbols (7.1%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 10/14 symbols (71.4%) - 9 successful calculations
market_type_scanner - INFO - Pivot point calculation progress: 14/14 symbols (100.0%) - 13 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 14/14 symbols successful (100.0% success rate)
market_type_scanner - INFO - Market data contains 14/14 symbols with pivot data
market_type_scanner - INFO - Built pivot data map with 1 entries from 1 symbols
pivot_point_integration - INFO - Filtered to top 1 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 1 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250723_001142.csv
report_generator - INFO - Report contains 1 symbols
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250723_001142.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250723_001142.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250723_001142.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 1
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250723_001142.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250723_001142.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250723_001142.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-23 00:11:42
unified_scanner - INFO - Total symbols found: 1
unified_scanner - INFO -   - OPTIONS symbols: 1
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250723_001142.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-23 00:24:21
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-23 00:24:21
unified_scanner - INFO - Enabled market types: OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 1 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Reusing existing Fyers authentication for pivot point scanning
market_type_scanner - INFO - Pivot point mode: Processing specific symbols: ['NIFTY']
market_type_scanner - INFO - Delta-based filtering: Created delta map with 14 entries
market_type_scanner - INFO - Pivot point mode: Added 14 options for NIFTY
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 14
market_type_scanner - INFO - Pivot point mode: Fetching market data for 14 OPTIONS symbols
market_type_scanner - INFO - Standard fetching for 14 symbols
fyers_client - INFO - Quote fetching progress: 14/14 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 14/14 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 14/14 symbols passed
market_type_scanner - INFO - LTP filter: 14/14 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 14 symbols
market_type_scanner - INFO - Pivot point calculation progress: 1/14 symbols (7.1%) - 0 successful calculations
fyers_connect - INFO - Loaded 124 INDEX symbols from NSE_CM.csv
fyers_connect - INFO - Created dynamic INDEX symbol mapping with 226 entries
market_type_scanner - INFO - Pivot point calculation progress: 10/14 symbols (71.4%) - 9 successful calculations
market_type_scanner - INFO - Pivot point calculation progress: 14/14 symbols (100.0%) - 13 successful calculations
market_type_scanner - INFO - Pivot point calculation completed: 14/14 symbols successful (100.0% success rate)
market_type_scanner - INFO - Market data contains 14/14 symbols with pivot data
market_type_scanner - INFO - Built pivot data map with 1 entries from 1 symbols
pivot_point_integration - INFO - Filtered to top 1 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 1 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlying symbols...
fyers_client - INFO - Quote fetching progress: 1/1 symbols (100.0%) - Batch 1/1 - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Spot price retrieval completed: 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250723_002428.csv
report_generator - INFO - Report contains 1 symbols
report_generator - WARNING - Found 1 unpaired symbols
report_generator - INFO - Sorted 1 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 1 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250723_002428.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250723_002428.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250723_002428.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 1
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250723_002428.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250723_002428.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250723_002429.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-23 00:24:29
unified_scanner - INFO - Total symbols found: 1
unified_scanner - INFO -   - OPTIONS symbols: 1
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250723_002429.txt
