#!/usr/bin/env python3
"""
Test script to debug weekly symbol parsing issues.
"""

import re
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_weekly_patterns():
    """Test different regex patterns for weekly NIFTY options."""
    print("=== Testing Weekly Symbol Parsing Patterns ===\n")

    test_symbols = [
        'NIFTY2572425000PE',  # July 24, 2025 - The problematic symbol
        'NIFTY2572425050CE',  # July 24, 2025
        'NIFTY2572425100CE',  # July 24, 2025
    ]

    # Current patterns from the code
    patterns = {
        'current_nifty': re.compile(r'^(NIFTY)(\d{2})([1-9]\d{2})(\d{5})(CE|PE)$'),
        'fallback': re.compile(r'^([A-Z0-9&]+)(\d{2})([1-9]\d{2})(\d{4,6})(CE|PE)$'),
        'flexible_strike': re.compile(r'^(NIFTY)(\d{2})([1-9]\d{2})(\d{4,6})(CE|PE)$'),
        'any_mdd': re.compile(r'^(NIFTY)(\d{2})(\d{3})(\d{4,6})(CE|PE)$'),
    }

    for symbol in test_symbols:
        print(f"Testing symbol: {symbol}")

        for pattern_name, pattern in patterns.items():
            match = pattern.match(symbol)
            if match:
                print(f"  ✓ {pattern_name}: {match.groups()}")
                
                underlying, year, date_part, strike, option_type = match.groups()
                print(f"     Underlying: {underlying}")
                print(f"     Year: {year}")
                print(f"     Date part: {date_part} (should be 724 for July 24)")
                print(f"     Strike: {strike}")
                print(f"     Option type: {option_type}")

                # Parse date part (MDD format where M=month, DD=day)
                if len(date_part) == 3:
                    month = date_part[0]  # First digit is month
                    day = date_part[1:3]  # Last two digits are day
                    print(f"     Parsed: Month={month}, Day={day}")

                    # Convert month to full format
                    month_full = f"0{month}" if len(month) == 1 else month
                    print(f"     Full date: 20{year}-{month_full}-{day} (should be 2025-07-24)")

            else:
                print(f"  ✗ {pattern_name}: No match")
        print()

def test_symbol_breakdown():
    """Test manual breakdown of the problematic symbol."""
    print("=== Manual Symbol Breakdown ===\n")
    
    symbol = 'NIFTY2572425000PE'
    print(f"Symbol: {symbol}")
    print(f"Length: {len(symbol)}")
    
    # Manual breakdown
    if symbol.startswith('NIFTY'):
        remaining = symbol[5:]  # Remove 'NIFTY'
        print(f"After removing 'NIFTY': {remaining}")
        
        if len(remaining) >= 2:
            year = remaining[:2]
            remaining = remaining[2:]
            print(f"Year: {year}, Remaining: {remaining}")
            
            if len(remaining) >= 3:
                mdd = remaining[:3]
                remaining = remaining[3:]
                print(f"MDD: {mdd}, Remaining: {remaining}")
                
                if remaining.endswith('PE') or remaining.endswith('CE'):
                    option_type = remaining[-2:]
                    strike = remaining[:-2]
                    print(f"Strike: {strike}, Option Type: {option_type}")
                    
                    print(f"\nFinal breakdown:")
                    print(f"  Underlying: NIFTY")
                    print(f"  Year: {year}")
                    print(f"  MDD: {mdd}")
                    print(f"  Strike: {strike}")
                    print(f"  Option Type: {option_type}")

if __name__ == "__main__":
    test_weekly_patterns()
    test_symbol_breakdown()
