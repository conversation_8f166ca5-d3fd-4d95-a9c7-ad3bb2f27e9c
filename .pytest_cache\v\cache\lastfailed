{"test/test_performance_optimizations.py::TestPerformanceOptimizations::test_option_chain_caching": true, "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_memory_optimization_large_dataset": true, "test/test_performance_optimizations.py::TestPerformanceOptimizations::test_batch_processing_optimization": true, "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_market_data_to_filtered_symbol_conversion": true, "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_monthly_expiry_configuration": true, "test/test_nifty_options_comprehensive.py::TestNiftyOptionsComprehensive::test_symbol_parsing_edge_cases": true}