#!/usr/bin/env python3
"""
Debug script to trace why NSE:NIFTY2572425000PE is not appearing in reports.
"""

import logging
import sys
import os
from datetime import datetime, date

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import Config<PERSON>oader
from universal_symbol_parser import UniversalSymbolParser
from option_utils import is_weekly_expiry_valid, get_current_week_expiry, get_next_week_expiry

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_symbol_parsing():
    """Test if the symbol can be parsed correctly."""
    print("=== Testing Symbol Parsing ===\n")
    
    config = ConfigLoader('config.yaml')
    parser = UniversalSymbolParser(config, ['NIFTY'])
    
    test_symbol = 'NSE:NIFTY2572425000PE'
    print(f"Testing symbol: {test_symbol}")
    
    # Test parsing
    parsed = parser.parse_symbol(test_symbol, 'NSE_FO.csv')
    if parsed:
        print(f"✓ Symbol parsed successfully:")
        print(f"  - Symbol: {parsed.symbol}")
        print(f"  - Market Type: {parsed.market_type}")
        print(f"  - Underlying: {parsed.underlying}")
        print(f"  - Expiry Year: {parsed.expiry_year}")
        print(f"  - Expiry Month: {parsed.expiry_month}")
        print(f"  - Strike Price: {parsed.strike_price}")
        print(f"  - Option Type: {parsed.option_type}")
        return parsed
    else:
        print("✗ Symbol parsing failed")
        return None

def test_weekly_expiry_validation(parsed_symbol):
    """Test weekly expiry validation."""
    print("\n=== Testing Weekly Expiry Validation ===\n")
    
    if not parsed_symbol:
        print("No parsed symbol to test")
        return False
    
    print(f"Expiry Year: {parsed_symbol.expiry_year}")
    print(f"Expiry Month: {parsed_symbol.expiry_month}")
    
    # Test weekly expiry validation
    if parsed_symbol.expiry_month and '_' in parsed_symbol.expiry_month:
        is_valid = is_weekly_expiry_valid(parsed_symbol.expiry_year, parsed_symbol.expiry_month)
        print(f"Weekly expiry validation: {is_valid}")
        
        # Get current and next week expiry for comparison
        current_expiry = get_current_week_expiry()
        next_expiry = get_next_week_expiry()
        print(f"Current week expiry: {current_expiry}")
        print(f"Next week expiry: {next_expiry}")
        
        # Parse the symbol's expiry date
        try:
            month_str, day_str = parsed_symbol.expiry_month.split('_')
            month = int(month_str)
            day = int(day_str)
            year = 2000 + int(parsed_symbol.expiry_year)
            symbol_expiry = date(year, month, day)
            print(f"Symbol expiry date: {symbol_expiry}")
            
            print(f"Is current week: {symbol_expiry == current_expiry}")
            print(f"Is next week: {symbol_expiry == next_expiry}")
            
        except Exception as e:
            print(f"Error parsing expiry date: {e}")
            
        return is_valid
    else:
        print("Not a weekly option (no underscore in expiry_month)")
        return False

def test_volume_and_ltp_filters():
    """Test if the symbol would pass volume and LTP filters."""
    print("\n=== Testing Volume and LTP Filters ===\n")

    config = ConfigLoader('config.yaml')
    min_volume = config.min_volume
    max_volume = config.max_volume
    min_ltp = config.min_ltp_price
    max_ltp = config.max_ltp_price

    print(f"Volume filter: {min_volume} - {max_volume}")
    print(f"LTP filter: {min_ltp} - {max_ltp}")

    # Note: We can't test actual market data without API calls
    print("Note: Actual volume and LTP data would need to be fetched from API")

def main():
    """Main debug function."""
    print("=== Debugging NSE:NIFTY2572425000PE Filtering Issue ===\n")
    
    # Test symbol parsing
    parsed_symbol = test_symbol_parsing()
    
    # Test weekly expiry validation
    if parsed_symbol:
        test_weekly_expiry_validation(parsed_symbol)
    
    # Test filters
    test_volume_and_ltp_filters()
    
    print("\n=== Debug Complete ===")

if __name__ == "__main__":
    main()
