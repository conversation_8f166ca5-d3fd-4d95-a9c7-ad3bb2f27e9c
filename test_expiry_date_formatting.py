#!/usr/bin/env python3
"""
Test script to verify expiry date formatting fixes.
"""

import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config_loader import Config<PERSON>oader
from report_generator import ReportGenerator
from market_type_scanner import FilteredSymbol, MarketData

class MockSymbol:
    """Mock symbol for testing."""
    def __init__(self, symbol, market_type, expiry_year, expiry_month, strike_price=None, option_type=None):
        self.symbol = symbol
        self.market_type = market_type
        self.expiry_year = expiry_year
        self.expiry_month = expiry_month
        self.strike_price = strike_price
        self.option_type = option_type

def test_expiry_date_formatting():
    """Test expiry date formatting for both weekly and monthly options."""
    print("=== Testing Expiry Date Formatting ===\n")
    
    config = ConfigLoader('config.yaml')
    report_gen = ReportGenerator("reports", config)
    
    # Test cases
    test_cases = [
        # Weekly options (MM_DD format)
        {
            'name': 'Weekly NIFTY PE (current issue)',
            'symbol': MockSymbol('NIFTY2572425000PE', 'OPTIONS', '25', '07_24', 25000.0, 'PE'),
            'expected': '2025-07-24'
        },
        {
            'name': 'Weekly NIFTY CE',
            'symbol': MockSymbol('NIFTY2572425050CE', 'OPTIONS', '25', '07_24', 25050.0, 'CE'),
            'expected': '2025-07-24'
        },
        {
            'name': 'Weekly NIFTY next week',
            'symbol': MockSymbol('NIFTY2573125100CE', 'OPTIONS', '25', '07_31', 25100.0, 'CE'),
            'expected': '2025-07-31'
        },
        # Monthly options (month name format)
        {
            'name': 'Monthly NIFTY JUL',
            'symbol': MockSymbol('NIFTY25JUL25000PE', 'OPTIONS', '25', 'JUL', 25000.0, 'PE'),
            'expected': '2025-07-31'  # Last Thursday of July 2025
        },
        {
            'name': 'Monthly NIFTY AUG',
            'symbol': MockSymbol('NIFTY25AUG25000CE', 'OPTIONS', '25', 'AUG', 25000.0, 'CE'),
            'expected': '2025-08-28'  # Last Thursday of August 2025
        }
    ]
    
    print("Testing with pivot_enabled=True (should use YYYY-MM-DD format):")
    for test_case in test_cases:
        symbol = test_case['symbol']
        expected = test_case['expected']
        
        result = report_gen._format_expiry_date_for_pivot_mode(symbol, pivot_enabled=True)
        
        status = "✓" if result == expected else "✗"
        print(f"  {status} {test_case['name']}")
        print(f"    Symbol: {symbol.symbol}")
        print(f"    Expiry Year: {symbol.expiry_year}")
        print(f"    Expiry Month: {symbol.expiry_month}")
        print(f"    Expected: {expected}")
        print(f"    Got: {result}")
        
        if result != expected:
            print(f"    ERROR: Mismatch!")
        print()
    
    print("Testing with pivot_enabled=False (should use original format):")
    for test_case in test_cases[:2]:  # Test only weekly options for original format
        symbol = test_case['symbol']
        
        result = report_gen._format_expiry_date_for_pivot_mode(symbol, pivot_enabled=False)
        expected_original = f"{symbol.expiry_month}{int(symbol.expiry_year):02d}"
        
        status = "✓" if result == expected_original else "✗"
        print(f"  {status} {test_case['name']} (original format)")
        print(f"    Expected: {expected_original}")
        print(f"    Got: {result}")
        print()

def test_year_conversion():
    """Test year conversion logic."""
    print("=== Testing Year Conversion ===\n")
    
    test_years = ['25', '24', '26', '50', '99', '00', '01']
    
    for year_str in test_years:
        year_2digit = int(year_str)
        year_4digit = 2000 + year_2digit if year_2digit < 50 else 1900 + year_2digit
        print(f"  {year_str} -> {year_4digit}")

if __name__ == "__main__":
    test_expiry_date_formatting()
    test_year_conversion()
